# 网站图片水印功能完整实现指南

## 🎯 功能概述

这是一个完整的图片水印功能实现方案，可以根据用户积分自动决定是否添加水印。当用户积分不足时，系统会自动为生成的图片添加水印；当用户积分充足时，可以选择是否显示水印。

## 🏗️ 系统架构

### 技术流程
```
用户上传图片 → API生成图片 → 前端检查用户积分 → 根据积分添加水印 → 显示最终结果
```

### 核心组件
1. **前端水印处理**：使用Canvas API在客户端添加水印
2. **积分检查系统**：实时检查用户积分决定水印策略
3. **图片代理服务**：解决跨域图片加载问题
4. **文件上传服务**：将水印图片保存到云存储

## 📁 需要创建的文件

### 1. 水印工具函数 (`utils/watermark.ts`)

这是核心的水印处理文件，包含所有水印相关的功能：

```typescript
// 水印处理工具函数
export async function addWatermarkToImage(imageUrl: string): Promise<string> {
  return new Promise((resolve, reject) => {
    console.log('🖼️ Loading image for watermark:', imageUrl.substring(0, 50) + '...');

    // 首先检查是否需要水印处理
    shouldAddWatermark().then(needsWatermark => {
      if (needsWatermark) {
        // 用户积分不足，需要添加水印
        loadImageAndAddWatermark(imageUrl, resolve, reject);
        return;
      } else {
        // 用户积分足够，直接返回原图
        resolve(imageUrl);
      }
    }).catch(error => {
      // 如果检查失败，为安全起见添加水印
      loadImageAndAddWatermark(imageUrl, resolve, reject);
    });
  });
}

// 检查用户积分并决定是否需要添加水印
export async function shouldAddWatermark(): Promise<boolean> {
  try {
    console.log('🔍 Checking user credits for watermark...');
    const response = await fetch('/api/credits');
    if (response.ok) {
      const userCredits = await response.json();
      console.log('💰 User credits:', userCredits.left_credits);
      const needsWatermark = userCredits.left_credits <= 5;
      console.log('🎨 Needs watermark:', needsWatermark);
      return needsWatermark;
    }
    console.warn('⚠️ Credits API response not ok, defaulting to add watermark');
    return true; // 如果获取积分失败，默认添加水印
  } catch (error) {
    console.error('❌ Error checking user credits:', error);
    return true; // 如果获取积分失败，默认添加水印
  }
}

// 处理图片数组，根据用户积分添加水印
export async function processImagesWithWatermark(imageUrls: string[], taskId?: string): Promise<string[]> {
  console.log('🖼️ Processing images with watermark check...', imageUrls.length, 'images');
  const needsWatermark = await shouldAddWatermark();

  if (!needsWatermark) {
    console.log('✅ User has sufficient credits, returning original images');
    return imageUrls; // 用户积分足够，返回原图
  }

  console.log('🎨 User needs watermark, processing images...');

  // 串行处理图片，确保数据库更新的顺序
  const watermarkedImages: string[] = [];

  for (let index = 0; index < imageUrls.length; index++) {
    const imageUrl = imageUrls[index];
    try {
      const watermarkedImage = await addWatermarkToImage(imageUrl);

      // 如果有taskId，上传水印图片到数据库
      if (taskId) {
        try {
          console.log(`💾 Uploading watermarked image ${index + 1} to database...`);
          const uploadResponse = await fetch('/api/upload-watermarked-image', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              imageData: watermarkedImage,
              taskId: taskId,
              originalUrl: imageUrl,
              imageIndex: index
            }),
          });

          if (uploadResponse.ok) {
            const result = await uploadResponse.json();
            console.log(`✅ Watermarked image ${index + 1} uploaded to database:`, result.watermarkedUrl);
            watermarkedImages.push(result.watermarkedUrl);
          } else {
            watermarkedImages.push(watermarkedImage);
          }
        } catch (uploadError) {
          console.error(`❌ Error uploading watermarked image ${index + 1}:`, uploadError);
          watermarkedImages.push(watermarkedImage);
        }
      } else {
        // 没有taskId，直接返回base64
        watermarkedImages.push(watermarkedImage);
      }

    } catch (error) {
      watermarkedImages.push(imageUrl); // 如果添加水印失败，返回原图
    }
  }

  console.log('🎉 Watermark processing and upload completed');
  return watermarkedImages;
}
// 加载图片并添加水印的核心函数
function loadImageAndAddWatermark(imageUrl: string, resolve: (value: string) => void, reject: (reason?: any) => void) {
  // 检查是否需要使用代理 - 对于跨域图片总是使用代理
  const needsProxy = imageUrl.startsWith('https://img.kontext-dev.com/') ||
                     imageUrl.startsWith('https://your-domain.com/');

  let finalImageUrl = imageUrl;
  if (needsProxy) {
    // 使用代理API来避免跨域问题
    finalImageUrl = `/api/proxy-image?url=${encodeURIComponent(imageUrl)}&download=false`;
  }

  const img = new Image();

  // 设置跨域策略
  if (!needsProxy) {
    img.crossOrigin = 'anonymous';
  }

  img.onload = function() {
    console.log('✅ Image loaded successfully, adding watermark...');
    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        createWatermarkPlaceholder(imageUrl, resolve, reject);
        return;
      }

      canvas.width = img.width;
      canvas.height = img.height;

      // 绘制原始图片
      ctx.drawImage(img, 0, 0);

      // 添加水印
      addWatermarkToCanvas(ctx, canvas.width, canvas.height, 'your-website.com');

      // 返回带水印的base64图片
      const watermarkedData = canvas.toDataURL('image/png');
      resolve(watermarkedData);

    } catch (error) {
      createWatermarkPlaceholder(imageUrl, resolve, reject);
    }
  };

  img.onerror = function(error) {
    if (needsProxy) {
      // 如果代理失败，尝试直接加载
      const directImg = new Image();
      directImg.crossOrigin = 'anonymous';
      directImg.onload = img.onload;
      directImg.onerror = function() {
        createWatermarkPlaceholder(imageUrl, resolve, reject);
      };
      directImg.src = imageUrl;
    } else {
      createWatermarkPlaceholder(imageUrl, resolve, reject);
    }
  };

  // 尝试加载图片
  img.src = finalImageUrl;
}

// 在Canvas上添加水印的函数
function addWatermarkToCanvas(ctx: CanvasRenderingContext2D, width: number, height: number, text: string) {
  // 保存当前状态
  ctx.save();

  // 设置水印样式，字体大小根据图片尺寸自适应
  const fontSize = Math.max(width, height) * 0.025; // 图片尺寸的2.5%
  ctx.font = `bold ${fontSize}px Arial`;
  ctx.fillStyle = 'rgba(255, 255, 255, 0.7)'; // 白色，70%透明度
  ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)'; // 黑色边框，50%透明度
  ctx.lineWidth = 2;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';

  // 计算水印间距
  const baseSpacing = 300;
  const spacingX = Math.max(baseSpacing, width * 0.25);  // 宽度的25%，最小300px
  const spacingY = Math.max(baseSpacing * 0.7, height * 0.2); // 高度的20%，最小210px

  // 旋转角度（-45度）
  const angle = -Math.PI / 4;

  // 计算网格数量
  const cols = Math.ceil(width / spacingX) + 3;
  const rows = Math.ceil(height / spacingY) + 3;

  // 计算起始偏移量，使水印居中
  const startX = -(cols * spacingX - width) / 2;
  const startY = -(rows * spacingY - height) / 2;

  // 在网格中添加水印
  for (let row = 0; row < rows; row++) {
    for (let col = 0; col < cols; col++) {
      ctx.save();

      const x = startX + col * spacingX;
      const y = startY + row * spacingY;

      // 只在合理范围内绘制水印
      if (x > -spacingX && x < width + spacingX &&
          y > -spacingY && y < height + spacingY) {

        ctx.translate(x, y);
        ctx.rotate(angle);

        ctx.strokeText(text, 0, 0);
        ctx.fillText(text, 0, 0);
      }

      ctx.restore();
    }
  }

  // 恢复状态
  ctx.restore();
}

// 创建水印占位符（当图片加载失败时使用）
function createWatermarkPlaceholder(imageUrl: string, resolve: (value: string) => void, reject: (reason?: any) => void) {
  console.log('🎨 Creating watermark placeholder for:', imageUrl.substring(0, 50) + '...');

  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    reject(new Error('Failed to get canvas context'));
    return;
  }

  // 设置标准尺寸
  canvas.width = 1024;
  canvas.height = 1024;

  // 创建渐变背景
  const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
  gradient.addColorStop(0, '#f0f0f0');
  gradient.addColorStop(1, '#e0e0e0');
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  // 添加水印
  addWatermarkToCanvas(ctx, canvas.width, canvas.height, 'your-website.com');

  // 添加提示文字
  ctx.save();
  ctx.font = 'bold 32px Arial';
  ctx.fillStyle = 'rgba(100, 100, 100, 0.8)';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText('Watermarked Image', canvas.width / 2, canvas.height / 2);
  ctx.restore();

  const watermarkedData = canvas.toDataURL('image/png');
  console.log('✅ Watermark placeholder created');
  resolve(watermarkedData);
}
### 2. 用户积分API (`app/api/credits/route.ts`)

这个API用于检查用户的剩余积分：

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getUserUuid } from '@/services/auth'; // 根据你的认证系统调整

export async function GET(req: NextRequest) {
  try {
    // 获取用户UUID
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // 从数据库获取用户积分
    // 这里需要根据你的数据库结构调整
    const userCredits = await getUserCredits(userUuid);

    return NextResponse.json({
      left_credits: userCredits.left_credits,
      total_credits: userCredits.total_credits
    });

  } catch (error) {
    console.error('Error fetching user credits:', error);
    return NextResponse.json(
      { error: 'Failed to fetch credits' },
      { status: 500 }
    );
  }
}

// 获取用户积分的函数（需要根据你的数据库调整）
async function getUserCredits(userUuid: string) {
  // 示例：从数据库查询用户积分
  // const { data, error } = await supabase
  //   .from('users')
  //   .select('left_credits, total_credits')
  //   .eq('uuid', userUuid)
  //   .single();

  // 返回示例数据，实际使用时请替换为真实的数据库查询
  return {
    left_credits: 10, // 用户剩余积分
    total_credits: 100 // 用户总积分
  };
}
```

### 3. 图片代理API (`app/api/proxy-image/route.ts`)

这个API用于解决跨域图片加载问题：

```typescript
import { NextRequest, NextResponse } from 'next/server';

export const runtime = 'edge';

/**
 * 图片代理API，用于解决跨域下载问题
 * 接收图片URL作为查询参数，然后获取图片内容并返回
 */
export async function GET(request: NextRequest) {
  try {
    // 从URL中解析查询参数
    const url = new URL(request.url).searchParams.get('url');

    if (!url) {
      return new NextResponse('Missing URL parameter', { status: 400 });
    }

    // 确保URL是有效的
    let imageUrl: string;
    try {
      imageUrl = decodeURIComponent(url);
      new URL(imageUrl); // 验证URL格式
    } catch (error) {
      console.error('Invalid image URL:', error);
      return new NextResponse('Invalid image URL', { status: 400 });
    }

    console.log('🔄 Proxying image request:', imageUrl.substring(0, 100) + '...');

    // 获取图片
    const imageResponse = await fetch(imageUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    if (!imageResponse.ok) {
      console.error('❌ Failed to fetch image:', imageResponse.status, imageResponse.statusText);
      return new NextResponse(`Failed to fetch image: ${imageResponse.statusText}`, {
        status: imageResponse.status
      });
    }

    // 获取图片数据
    const imageBuffer = await imageResponse.arrayBuffer();
    const contentType = imageResponse.headers.get('content-type') || 'image/jpeg';

    console.log('✅ Image proxied successfully, size:', imageBuffer.byteLength, 'bytes');

    // 返回图片数据
    return new NextResponse(imageBuffer, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000', // 缓存1年
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    console.error('❌ Error in image proxy:', error);
    return new NextResponse('Internal server error', { status: 500 });
  }
}
### 4. 上传水印图片API (`app/api/upload-watermarked-image/route.ts`)

这个API用于将水印图片上传到云存储：

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { getUserUuid } from '@/services/auth';

// 配置S3客户端（适用于AWS S3或兼容的服务如Cloudflare R2）
const s3Client = new S3Client({
  region: process.env.STORAGE_REGION || 'auto',
  endpoint: process.env.STORAGE_ENDPOINT,
  credentials: {
    accessKeyId: process.env.STORAGE_ACCESS_KEY || '',
    secretAccessKey: process.env.STORAGE_SECRET_KEY || '',
  },
});

export async function POST(req: NextRequest) {
  try {
    // 检查用户认证
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { imageData, taskId, originalUrl, imageIndex = 0 } = await req.json();

    if (!imageData || !taskId) {
      return NextResponse.json(
        { error: 'Image data and task ID are required' },
        { status: 400 }
      );
    }

    // 从base64数据中提取图片数据
    const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, '');
    const imageBuffer = Buffer.from(base64Data, 'base64');

    // 生成唯一的文件名
    const timestamp = Date.now();
    const filename = `watermarked-${taskId}-${imageIndex}-${timestamp}.png`;

    console.log(`Uploading watermarked image: ${filename}`);

    // 上传到云存储
    const uploadCommand = new PutObjectCommand({
      Bucket: process.env.STORAGE_BUCKET || "",
      Key: filename,
      Body: imageBuffer,
      ContentType: 'image/png',
    });

    await s3Client.send(uploadCommand);

    // 构建图片URL
    const watermarkedUrl = `${process.env.STORAGE_PUBLIC_URL}/${filename}`;

    console.log('✅ Watermarked image uploaded successfully:', watermarkedUrl);

    // 只在第一张图片时更新数据库中的generated_image_url
    if (imageIndex === 0) {
      // 更新数据库记录
      await updateDatabaseRecord(taskId, userUuid, watermarkedUrl);
    }

    return NextResponse.json({
      success: true,
      watermarkedUrl: watermarkedUrl,
      filename: filename
    });

  } catch (error) {
    console.error('Error uploading watermarked image:', error);
    return NextResponse.json(
      {
        error: 'Failed to upload watermarked image',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// 更新数据库记录的函数
async function updateDatabaseRecord(taskId: string, userUuid: string, watermarkedUrl: string) {
  try {
    // 这里需要根据你的数据库结构调整
    // 示例使用Supabase：
    // const supabase = getSupabaseClient();
    // const { error } = await supabase
    //   .from("your_table_name")
    //   .update({
    //     generated_image_url: watermarkedUrl,
    //     status: 'COMPLETED',
    //     completed_at: new Date().toISOString()
    //   })
    //   .eq("task_id", taskId)
    //   .eq("user_uuid", userUuid);

    console.log('Database record updated for task:', taskId);
  } catch (error) {
    console.error('Error updating database record:', error);
  }
}
```

### 5. 替换水印图片API (`app/api/replace-with-watermarked-image/route.ts`)

这个API用于用水印图片替换原始图片：

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';

const s3Client = new S3Client({
  region: process.env.STORAGE_REGION || 'auto',
  endpoint: process.env.STORAGE_ENDPOINT,
  credentials: {
    accessKeyId: process.env.STORAGE_ACCESS_KEY || '',
    secretAccessKey: process.env.STORAGE_SECRET_KEY || '',
  },
});

/**
 * 用带水印的图片替换云存储中的原始图片
 */
export async function POST(request: NextRequest) {
  try {
    const { imageData, filename, originalUrl } = await request.json();

    if (!imageData || !filename) {
      return NextResponse.json({ error: 'Missing imageData or filename' }, { status: 400 });
    }

    console.log('🔄 Replacing original image with watermarked version:', filename);

    // 从base64数据中提取图片数据
    const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, '');
    const imageBuffer = Buffer.from(base64Data, 'base64');

    // 上传到云存储，替换原来的文件
    const uploadCommand = new PutObjectCommand({
      Bucket: process.env.STORAGE_BUCKET || "",
      Key: filename,
      Body: imageBuffer,
      ContentType: 'image/png',
    });

    await s3Client.send(uploadCommand);

    console.log('✅ Original image replaced with watermarked version:', filename);

    return NextResponse.json({
      success: true,
      url: originalUrl,
      message: 'Image replaced with watermarked version successfully'
    });

  } catch (error) {
    console.error('❌ Error replacing image with watermarked version:', error);
    return NextResponse.json({
      error: 'Failed to replace image with watermarked version',
      details: (error as Error).message
    }, { status: 500 });
  }
}
## 🎨 前端组件集成

### 6. 在React组件中使用水印功能

以下是如何在你的React组件中集成水印功能的完整示例：

```typescript
"use client";

import { useState, useEffect } from 'react';
import { processImagesWithWatermark, shouldAddWatermark } from '@/utils/watermark';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { v4 as uuidv4 } from 'uuid';

interface ImageGeneratorProps {
  title?: string;
}

export default function ImageGenerator({ title = "AI图片生成器" }: ImageGeneratorProps) {
  // 状态管理
  const [generatedImages, setGeneratedImages] = useState<string[]>([]);
  const [originalImageUrls, setOriginalImageUrls] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showWatermark, setShowWatermark] = useState(true);
  const [userCredits, setUserCredits] = useState<number | null>(null);
  const [currentTaskId, setCurrentTaskId] = useState<string>('');

  // 检查用户积分
  useEffect(() => {
    checkUserCredits();
  }, []);

  const checkUserCredits = async () => {
    try {
      const response = await fetch('/api/credits');
      if (response.ok) {
        const credits = await response.json();
        setUserCredits(credits.left_credits);

        // 如果用户积分不足，强制显示水印
        if (credits.left_credits <= 5) {
          setShowWatermark(true);
        }
      }
    } catch (error) {
      console.error('Error checking user credits:', error);
    }
  };

  // 生成图片的主要函数
  const generateImages = async (prompt: string) => {
    if (!prompt.trim()) {
      toast.error('请输入图片描述');
      return;
    }

    setIsGenerating(true);
    const taskId = uuidv4();
    setCurrentTaskId(taskId);

    try {
      // 调用你的图片生成API
      const response = await fetch('/api/generate-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: prompt,
          taskId: taskId,
          // 其他参数...
        }),
      });

      if (!response.ok) {
        throw new Error('图片生成失败');
      }

      const data = await response.json();

      // 假设API返回的是图片URL数组
      const imageUrls = data.imageUrls || [data.imageUrl];

      // 保存原始图片URL
      setOriginalImageUrls(imageUrls);

      // 根据用户积分处理水印
      console.log('🎯 Starting watermark processing for generated images...');
      const processedImages = await processImagesWithWatermark(imageUrls, taskId);
      console.log('🎯 Watermark processing completed, setting images...');

      setGeneratedImages(processedImages);
      toast.success('图片生成成功！');

    } catch (error) {
      console.error('Error generating images:', error);
      toast.error('图片生成失败，请重试');
    } finally {
      setIsGenerating(false);
    }
  };

  // 水印切换处理
  const handleWatermarkToggle = async (checked: boolean) => {
    // 检查用户权限
    if (userCredits !== null && userCredits <= 5 && !checked) {
      toast.error('积分不足，无法移除水印。请升级账户。');
      return;
    }

    // 付费用户可以自由控制水印
    setShowWatermark(checked);

    // 如果有已生成的图片，重新处理它们
    if (originalImageUrls.length > 0) {
      try {
        let processedImages: string[];
        if (checked) {
          // 添加水印到原始图片
          processedImages = await processImagesWithWatermark(originalImageUrls, currentTaskId);
        } else {
          // 使用原始图片（无水印）
          processedImages = originalImageUrls;
        }
        setGeneratedImages(processedImages);
        toast.success(checked ? '已添加水印' : '已移除水印');
      } catch (error) {
        console.error('Error reprocessing images with watermark:', error);
        toast.error('水印处理失败，请重试');
      }
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold text-center mb-8">{title}</h1>

      {/* 水印控制开关 */}
      <div className="mb-6 flex items-center space-x-2">
        <Switch
          id="watermark-toggle"
          checked={showWatermark}
          onCheckedChange={handleWatermarkToggle}
          disabled={userCredits !== null && userCredits <= 5}
        />
        <Label htmlFor="watermark-toggle">
          显示水印
          {userCredits !== null && userCredits <= 5 && (
            <span className="text-sm text-gray-500 ml-2">
              (积分不足，无法移除)
            </span>
          )}
        </Label>
      </div>

      {/* 图片生成表单 */}
      <div className="mb-8">
        <textarea
          className="w-full p-3 border rounded-lg"
          placeholder="描述你想要生成的图片..."
          rows={3}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              generateImages(e.currentTarget.value);
            }
          }}
        />
        <Button
          onClick={(e) => {
            const textarea = e.currentTarget.previousElementSibling as HTMLTextAreaElement;
            generateImages(textarea.value);
          }}
          disabled={isGenerating}
          className="mt-3 w-full"
        >
          {isGenerating ? '生成中...' : '生成图片'}
        </Button>
      </div>

      {/* 显示生成的图片 */}
      {generatedImages.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {generatedImages.map((imageUrl, index) => (
            <div key={index} className="relative">
              <img
                src={imageUrl}
                alt={`Generated image ${index + 1}`}
                className="w-full h-auto rounded-lg shadow-lg"
              />
              <Button
                onClick={() => {
                  // 下载图片
                  const link = document.createElement('a');
                  link.href = imageUrl;
                  link.download = `generated-image-${index + 1}.png`;
                  link.click();
                }}
                className="absolute bottom-2 right-2"
                size="sm"
              >
                下载
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
## ⚙️ 环境配置

### 7. 环境变量配置 (`.env.local`)

在你的项目根目录创建 `.env.local` 文件，添加以下配置：

```bash
# 云存储配置（AWS S3 或 Cloudflare R2）
STORAGE_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
STORAGE_REGION=auto
STORAGE_BUCKET=your-bucket-name
STORAGE_ACCESS_KEY=your-access-key
STORAGE_SECRET_KEY=your-secret-key
STORAGE_PUBLIC_URL=https://your-domain.com

# 数据库配置（根据你使用的数据库调整）
DATABASE_URL=your-database-connection-string

# 应用配置
NEXT_PUBLIC_APP_URL=https://your-website.com
```

### 8. 依赖包安装

在你的项目中安装必要的依赖包：

```bash
# 安装AWS SDK（用于云存储）
npm install @aws-sdk/client-s3

# 安装UUID生成器
npm install uuid
npm install @types/uuid

# 如果使用Supabase作为数据库
npm install @supabase/supabase-js

# 如果使用NextAuth进行认证
npm install next-auth

# UI组件库（可选，根据你的需求）
npm install sonner  # 用于toast通知
```

## 🚀 部署和使用步骤

### 步骤1：创建文件结构

在你的Next.js项目中创建以下文件结构：

```
your-project/
├── utils/
│   └── watermark.ts                    # 水印工具函数
├── app/api/
│   ├── credits/
│   │   └── route.ts                    # 用户积分API
│   ├── proxy-image/
│   │   └── route.ts                    # 图片代理API
│   ├── upload-watermarked-image/
│   │   └── route.ts                    # 上传水印图片API
│   └── replace-with-watermarked-image/
│       └── route.ts                    # 替换水印图片API
├── components/
│   └── your-component.tsx              # 你的图片生成组件
└── .env.local                          # 环境变量配置
```

### 步骤2：配置云存储

1. **使用Cloudflare R2（推荐）：**
   - 登录Cloudflare Dashboard
   - 创建R2存储桶
   - 获取API令牌和访问密钥
   - 配置自定义域名（可选）

2. **使用AWS S3：**
   - 登录AWS控制台
   - 创建S3存储桶
   - 配置IAM用户和权限
   - 获取访问密钥

### 步骤3：配置数据库

根据你使用的数据库系统，创建用户表和图片记录表：

```sql
-- 用户表示例
CREATE TABLE users (
  uuid VARCHAR(36) PRIMARY KEY,
  email VARCHAR(255) UNIQUE,
  nickname VARCHAR(100),
  left_credits INTEGER DEFAULT 10,
  total_credits INTEGER DEFAULT 10,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 图片生成记录表示例
CREATE TABLE image_generations (
  id SERIAL PRIMARY KEY,
  task_id VARCHAR(36) UNIQUE,
  user_uuid VARCHAR(36) REFERENCES users(uuid),
  prompt TEXT,
  generated_image_url TEXT,
  original_image_url TEXT,
  status VARCHAR(20) DEFAULT 'PENDING',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP
);
```

### 步骤4：集成到现有组件

1. **导入水印工具函数：**
```typescript
import { processImagesWithWatermark, shouldAddWatermark } from '@/utils/watermark';
```

2. **在图片生成完成后调用水印处理：**
```typescript
// 在你的图片生成API调用成功后
const processedImages = await processImagesWithWatermark(imageUrls, taskId);
setGeneratedImages(processedImages);
```

3. **添加水印控制开关（可选）：**
```typescript
const handleWatermarkToggle = async (checked: boolean) => {
  if (userCredits <= 5 && !checked) {
    toast.error('积分不足，无法移除水印');
    return;
  }
  // 重新处理图片...
};
## 🧪 测试和调试

### 步骤5：创建测试页面

创建一个简单的测试页面来验证水印功能：

```html
<!-- public/test-watermark.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水印功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .result-image { max-width: 400px; margin: 10px 0; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>水印功能测试页面</h1>

    <div class="test-section">
        <h2>1. 测试用户积分检查</h2>
        <button onclick="testCreditsAPI()">检查用户积分</button>
        <div id="credits-result"></div>
    </div>

    <div class="test-section">
        <h2>2. 测试图片水印添加</h2>
        <input type="url" id="image-url" placeholder="输入图片URL" style="width: 400px; padding: 8px;">
        <button onclick="testWatermark()">添加水印</button>
        <div id="watermark-result"></div>
    </div>

    <div class="test-section">
        <h2>3. 测试图片代理</h2>
        <button onclick="testImageProxy()">测试图片代理</button>
        <div id="proxy-result"></div>
    </div>

    <div class="test-section">
        <h2>调试日志</h2>
        <div id="debug-log" class="log"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <script>
        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }

        // 测试用户积分API
        async function testCreditsAPI() {
            try {
                log('🔍 Testing credits API...');
                const response = await fetch('/api/credits');
                const data = await response.json();

                if (response.ok) {
                    log(`✅ Credits API success: ${data.left_credits} credits left`);
                    document.getElementById('credits-result').innerHTML =
                        `<p>剩余积分: ${data.left_credits}</p><p>总积分: ${data.total_credits}</p>`;
                } else {
                    log(`❌ Credits API error: ${data.error}`);
                    document.getElementById('credits-result').innerHTML =
                        `<p style="color: red;">错误: ${data.error}</p>`;
                }
            } catch (error) {
                log(`❌ Credits API exception: ${error.message}`);
                document.getElementById('credits-result').innerHTML =
                    `<p style="color: red;">异常: ${error.message}</p>`;
            }
        }

        // 测试水印功能
        async function testWatermark() {
            const imageUrl = document.getElementById('image-url').value;
            if (!imageUrl) {
                alert('请输入图片URL');
                return;
            }

            try {
                log(`🎨 Testing watermark for: ${imageUrl}`);
                const watermarkedImage = await addWatermarkWithCanvas(imageUrl);

                log('✅ Watermark added successfully');
                document.getElementById('watermark-result').innerHTML =
                    `<img src="${watermarkedImage}" class="result-image" alt="Watermarked image">`;
            } catch (error) {
                log(`❌ Watermark error: ${error.message}`);
                document.getElementById('watermark-result').innerHTML =
                    `<p style="color: red;">水印添加失败: ${error.message}</p>`;
            }
        }

        // 测试图片代理
        async function testImageProxy() {
            const testUrl = 'https://picsum.photos/400/300';
            try {
                log('🔄 Testing image proxy...');
                const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(testUrl)}`;

                const img = new Image();
                img.onload = () => {
                    log('✅ Image proxy test successful');
                    document.getElementById('proxy-result').innerHTML =
                        `<img src="${proxyUrl}" class="result-image" alt="Proxied image">`;
                };
                img.onerror = () => {
                    log('❌ Image proxy test failed');
                    document.getElementById('proxy-result').innerHTML =
                        `<p style="color: red;">图片代理测试失败</p>`;
                };
                img.src = proxyUrl;
            } catch (error) {
                log(`❌ Image proxy exception: ${error.message}`);
            }
        }

        // Canvas水印添加函数
        async function addWatermarkWithCanvas(imageUrl) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.crossOrigin = 'anonymous';

                img.onload = function() {
                    try {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        canvas.width = img.width;
                        canvas.height = img.height;

                        // 绘制原始图片
                        ctx.drawImage(img, 0, 0);

                        // 添加水印
                        addWatermarkToCanvas(ctx, canvas.width, canvas.height, 'your-website.com');

                        // 转换为data URL
                        const watermarkedData = canvas.toDataURL('image/png');
                        resolve(watermarkedData);
                    } catch (error) {
                        reject(error);
                    }
                };

                img.onerror = () => reject(new Error('Failed to load image'));
                img.src = imageUrl;
            });
        }

        // 在Canvas上添加水印
        function addWatermarkToCanvas(ctx, width, height, text) {
            ctx.save();

            const fontSize = Math.max(width, height) * 0.025;
            ctx.font = `bold ${fontSize}px Arial`;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.lineWidth = 2;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            const spacingX = Math.max(300, width * 0.25);
            const spacingY = Math.max(210, height * 0.2);
            const angle = -Math.PI / 4;

            const cols = Math.ceil(width / spacingX) + 3;
            const rows = Math.ceil(height / spacingY) + 3;

            const startX = -(cols * spacingX - width) / 2;
            const startY = -(rows * spacingY - height) / 2;

            for (let row = 0; row < rows; row++) {
                for (let col = 0; col < cols; col++) {
                    ctx.save();

                    const x = startX + col * spacingX;
                    const y = startY + row * spacingY;

                    if (x > -spacingX && x < width + spacingX &&
                        y > -spacingY && y < height + spacingY) {

                        ctx.translate(x, y);
                        ctx.rotate(angle);

                        ctx.strokeText(text, 0, 0);
                        ctx.fillText(text, 0, 0);
                    }

                    ctx.restore();
                }
            }

            ctx.restore();
        }

        // 页面加载时自动测试积分API
        window.onload = function() {
            log('🚀 Test page loaded');
            testCreditsAPI();
        };
    </script>
</body>
</html>
## 🔧 故障排除

### 常见问题和解决方案

#### 1. 跨域图片加载失败
**问题：** 图片无法加载，控制台显示CORS错误
**解决方案：**
```typescript
// 确保使用图片代理API
const needsProxy = imageUrl.startsWith('https://external-domain.com/');
if (needsProxy) {
  finalImageUrl = `/api/proxy-image?url=${encodeURIComponent(imageUrl)}`;
}

// 或者设置正确的CORS头
img.crossOrigin = 'anonymous';
```

#### 2. Canvas无法获取上下文
**问题：** `canvas.getContext('2d')` 返回null
**解决方案：**
```typescript
const ctx = canvas.getContext('2d');
if (!ctx) {
  // 创建备用的水印占位符
  createWatermarkPlaceholder(imageUrl, resolve, reject);
  return;
}
```

#### 3. 水印图片上传失败
**问题：** 上传到云存储时出现权限错误
**解决方案：**
```bash
# 检查环境变量配置
STORAGE_ACCESS_KEY=your-correct-access-key
STORAGE_SECRET_KEY=your-correct-secret-key
STORAGE_BUCKET=your-correct-bucket-name

# 确保存储桶权限正确配置
```

#### 4. 用户积分检查失败
**问题：** `/api/credits` 返回401或500错误
**解决方案：**
```typescript
// 在shouldAddWatermark函数中添加错误处理
export async function shouldAddWatermark(): Promise<boolean> {
  try {
    const response = await fetch('/api/credits');
    if (response.ok) {
      const userCredits = await response.json();
      return userCredits.left_credits <= 5;
    }
    // 如果API失败，默认添加水印（安全策略）
    return true;
  } catch (error) {
    console.error('❌ Error checking user credits:', error);
    return true; // 默认添加水印
  }
}
```

#### 5. 水印显示不清晰
**问题：** 水印文字模糊或不够明显
**解决方案：**
```typescript
// 调整水印样式参数
const fontSize = Math.max(width, height) * 0.03; // 增加字体大小
ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'; // 增加不透明度
ctx.strokeStyle = 'rgba(0, 0, 0, 0.6)'; // 增加边框不透明度
ctx.lineWidth = 3; // 增加边框宽度
```

### 性能优化建议

#### 1. 图片处理优化
```typescript
// 对大图片进行尺寸限制
if (img.width > 2048 || img.height > 2048) {
  const scale = Math.min(2048 / img.width, 2048 / img.height);
  canvas.width = img.width * scale;
  canvas.height = img.height * scale;
  ctx.scale(scale, scale);
}
```

#### 2. 批量处理优化
```typescript
// 使用串行处理避免并发过多
for (let index = 0; index < imageUrls.length; index++) {
  const watermarkedImage = await addWatermarkToImage(imageUrls[index]);
  watermarkedImages.push(watermarkedImage);
}
```

#### 3. 缓存策略
```typescript
// 在代理API中添加缓存头
return new NextResponse(imageBuffer, {
  headers: {
    'Content-Type': contentType,
    'Cache-Control': 'public, max-age=31536000', // 缓存1年
  },
});
```

## 📋 最佳实践

### 1. 安全考虑
- **默认添加水印：** 当积分检查失败时，始终默认添加水印
- **服务端验证：** 重要的积分检查应该在服务端进行
- **访问控制：** 确保只有认证用户可以访问相关API

### 2. 用户体验
- **渐进式加载：** 先显示原图，然后异步添加水印
- **错误处理：** 提供友好的错误提示和降级方案
- **性能监控：** 监控水印处理的耗时和成功率

### 3. 代码组织
- **模块化：** 将水印功能封装成独立的工具函数
- **类型安全：** 使用TypeScript确保类型安全
- **错误边界：** 在React组件中添加错误边界

### 4. 监控和日志
```typescript
// 添加详细的日志记录
console.log('🖼️ Processing images with watermark check...', imageUrls.length, 'images');
console.log('💰 User credits:', userCredits.left_credits);
console.log('🎨 Needs watermark:', needsWatermark);
console.log('✅ Watermark processing completed');
```

## 🎯 自定义配置

### 水印文字自定义
```typescript
// 在addWatermarkToCanvas函数中自定义水印文字
function addWatermarkToCanvas(ctx: CanvasRenderingContext2D, width: number, height: number, text: string) {
  // 可以根据不同的用户或场景使用不同的水印文字
  const watermarkText = text || 'your-default-watermark.com';

  // 可以添加多行水印
  const lines = [watermarkText, '© 2024'];
  lines.forEach((line, index) => {
    // 绘制每一行...
  });
}
```

### 水印样式自定义
```typescript
// 创建不同的水印样式
const watermarkStyles = {
  light: {
    fillStyle: 'rgba(255, 255, 255, 0.5)',
    strokeStyle: 'rgba(0, 0, 0, 0.3)',
  },
  dark: {
    fillStyle: 'rgba(0, 0, 0, 0.7)',
    strokeStyle: 'rgba(255, 255, 255, 0.5)',
  },
  colorful: {
    fillStyle: 'rgba(255, 0, 0, 0.6)',
    strokeStyle: 'rgba(255, 255, 255, 0.8)',
  }
};
```

### 积分阈值自定义
```typescript
// 可配置的积分阈值
const WATERMARK_THRESHOLD = process.env.WATERMARK_THRESHOLD ?
  parseInt(process.env.WATERMARK_THRESHOLD) : 5;

export async function shouldAddWatermark(): Promise<boolean> {
  // 使用可配置的阈值
  const needsWatermark = userCredits.left_credits <= WATERMARK_THRESHOLD;
  return needsWatermark;
}
## 🚀 快速开始指南

### 最简单的实现步骤

如果你想快速在现有项目中添加水印功能，按照以下步骤操作：

#### 1. 复制核心文件
将以下文件复制到你的项目中：
- `utils/watermark.ts` - 核心水印处理函数
- `app/api/credits/route.ts` - 用户积分检查API
- `app/api/proxy-image/route.ts` - 图片代理API

#### 2. 安装依赖
```bash
npm install @aws-sdk/client-s3 uuid @types/uuid
```

#### 3. 配置环境变量
```bash
# .env.local
STORAGE_ENDPOINT=your-storage-endpoint
STORAGE_BUCKET=your-bucket-name
STORAGE_ACCESS_KEY=your-access-key
STORAGE_SECRET_KEY=your-secret-key
STORAGE_PUBLIC_URL=your-public-url
```

#### 4. 在组件中使用
```typescript
import { processImagesWithWatermark } from '@/utils/watermark';

// 在图片生成完成后调用
const processedImages = await processImagesWithWatermark(imageUrls);
setGeneratedImages(processedImages);
```

#### 5. 测试功能
访问 `/test-watermark.html` 测试各项功能是否正常工作。

## 📝 总结

这个水印功能实现方案具有以下特点：

### ✅ 优势
1. **智能判断**：根据用户积分自动决定是否添加水印
2. **前端处理**：使用Canvas API在客户端处理，减少服务器负载
3. **跨域支持**：通过代理API解决跨域图片加载问题
4. **高度可定制**：支持自定义水印文字、样式和积分阈值
5. **错误处理**：完善的错误处理和降级方案
6. **性能优化**：支持批量处理和缓存策略

### 🎯 适用场景
- AI图片生成网站
- 图片编辑工具
- 内容创作平台
- 需要版权保护的图片服务

### 🔧 技术栈
- **前端**：Next.js + TypeScript + Canvas API
- **后端**：Next.js API Routes
- **存储**：AWS S3 / Cloudflare R2
- **数据库**：任何支持的数据库（MySQL、PostgreSQL、Supabase等）

## 🤝 支持和维护

### 常用命令
```bash
# 开发环境启动
npm run dev

# 构建生产版本
npm run build

# 启动生产服务
npm start

# 类型检查
npm run type-check
```

### 监控指标
建议监控以下指标：
- 水印处理成功率
- 图片加载失败率
- API响应时间
- 用户积分使用情况

### 版本更新
当需要更新水印功能时：
1. 备份现有配置
2. 更新相关文件
3. 测试所有功能
4. 逐步部署到生产环境

## 📞 技术支持

如果在实施过程中遇到问题，可以：

1. **检查控制台日志**：查看浏览器控制台和服务器日志
2. **使用测试页面**：通过测试页面验证各个功能模块
3. **查看网络请求**：检查API请求和响应是否正常
4. **验证环境配置**：确保所有环境变量配置正确

### 常见错误代码
- `401`: 用户未认证，检查认证系统
- `400`: 请求参数错误，检查API调用参数
- `500`: 服务器内部错误，检查服务器日志
- `CORS Error`: 跨域错误，使用图片代理API

---

**🎉 恭喜！你现在已经掌握了完整的图片水印功能实现方法。**

这个方案经过实际项目验证，可以直接用于生产环境。记住要根据你的具体需求调整配置参数，并在部署前充分测试所有功能。

如果你需要进一步的定制或有特殊需求，可以基于这个基础方案进行扩展。祝你的项目成功！🚀